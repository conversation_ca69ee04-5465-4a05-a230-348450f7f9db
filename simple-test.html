<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单测试 - H5游戏控制台检测绕过</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .warning { background-color: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 H5游戏控制台检测绕过 - 简单测试</h1>
        
        <div id="status" class="status warning">
            ⚠️ 正在初始化...
        </div>
        
        <div>
            <button onclick="loadGame()" id="loadBtn" disabled>加载游戏</button>
            <button onclick="testAll()">运行测试</button>
            <button onclick="openConsole()">安全打开控制台</button>
            <button onclick="clearLog()">清空日志</button>
        </div>
        
        <div id="log" class="log">正在加载组件...</div>
        
        <div style="margin-top: 20px;">
            <h3>📋 使用说明</h3>
            <ol>
                <li>页面会自动加载绕过脚本和游戏加载器</li>
                <li>等待初始化完成后，点击"加载游戏"</li>
                <li>现在可以安全地按F12打开开发者工具</li>
                <li>如果遇到问题，点击"运行测试"检查状态</li>
            </ol>
        </div>
    </div>

    <!-- 加载绕过脚本 -->
    <script src="bypass-console-detection.js"></script>
    <!-- 加载游戏加载器 -->
    <script src="webpack-game-loader.js"></script>
    
    <script>
        let gameLoaded = false;
        
        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[页面] ${message}`);
        }
        
        function updateStatus(message, type = 'warning') {
            const statusElement = document.getElementById('status');
            const icons = { success: '✅', error: '❌', warning: '⚠️' };
            statusElement.className = `status ${type}`;
            statusElement.textContent = `${icons[type]} ${message}`;
        }
        
        async function loadGame() {
            if (gameLoaded) {
                log('游戏已经加载过了');
                return;
            }
            
            try {
                log('开始加载游戏...');
                updateStatus('正在加载游戏...', 'warning');
                
                if (!window.gameLoader) {
                    throw new Error('游戏加载器未初始化');
                }
                
                await window.gameLoader.loadGame('game.js');
                gameLoaded = true;
                
                log('✅ 游戏加载成功！');
                updateStatus('游戏加载成功，控制台检测已被绕过！', 'success');
                
            } catch (error) {
                log(`❌ 加载失败: ${error.message}`);
                updateStatus(`加载失败: ${error.message}`, 'error');
            }
        }
        
        function testAll() {
            log('🧪 开始运行所有测试...');
            
            try {
                // 测试绕过工具
                if (window.bypassUtils && window.bypassUtils.test) {
                    const bypassResult = window.bypassUtils.test();
                    log(`绕过工具测试: ${bypassResult ? '✅ 通过' : '❌ 失败'}`);
                }
                
                // 测试游戏加载器
                if (window.gameLoader && window.gameLoader.test) {
                    const loaderResult = window.gameLoader.test();
                    log(`游戏加载器测试: ${loaderResult ? '✅ 通过' : '❌ 失败'}`);
                }
                
                // 基础功能测试
                log('基础功能测试:');
                
                const func = function() {};
                log(`  Function.toString: ${func.toString().length > 0 ? '✅' : '❌'}`);
                
                const obj = {};
                log(`  Constructor访问: ${typeof obj.constructor === 'function' ? '✅' : '❌'}`);
                
                log(`  Console对象: ${typeof console.log === 'function' ? '✅' : '❌'}`);
                
                // 环境检测
                log('环境检测:');
                log(`  Webpack环境: ${window.self ? '✅' : '❌'}`);
                log(`  绕过脚本: ${window.bypassUtils ? '✅' : '❌'}`);
                log(`  游戏加载器: ${window.gameLoader ? '✅' : '❌'}`);
                
                log('✅ 所有测试完成！');
                updateStatus('测试完成', 'success');
                
            } catch (error) {
                log(`❌ 测试过程中出错: ${error.message}`);
                updateStatus(`测试失败: ${error.message}`, 'error');
            }
        }
        
        function openConsole() {
            log('💡 提示：现在可以安全地按F12打开开发者工具了');
            log('控制台检测已被绕过，不会触发游戏的检测机制');
            updateStatus('可以安全打开开发者工具', 'success');
        }
        
        function clearLog() {
            document.getElementById('log').textContent = '';
            log('日志已清空');
        }
        
        // 页面初始化
        window.addEventListener('load', function() {
            log('页面加载完成');
            
            // 检查组件加载状态
            let allReady = true;
            
            if (window.bypassUtils) {
                log('✅ 绕过脚本加载成功');
            } else {
                log('❌ 绕过脚本加载失败');
                allReady = false;
            }
            
            if (window.gameLoader) {
                log('✅ 游戏加载器加载成功');
            } else {
                log('❌ 游戏加载器加载失败');
                allReady = false;
            }
            
            if (allReady) {
                log('✅ 所有组件加载完成，可以开始使用');
                updateStatus('所有组件就绪，可以加载游戏', 'success');
                document.getElementById('loadBtn').disabled = false;
            } else {
                log('❌ 部分组件加载失败');
                updateStatus('组件加载不完整', 'error');
            }
        });
        
        // 全局错误处理
        window.addEventListener('error', function(event) {
            if (event.error && event.error.message) {
                log(`❌ 全局错误: ${event.error.message}`);
            }
        });
        
        window.addEventListener('unhandledrejection', function(event) {
            log(`❌ 未处理的Promise拒绝: ${event.reason}`);
        });
    </script>
</body>
</html>
