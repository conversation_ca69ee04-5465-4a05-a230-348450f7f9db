<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>H5游戏 - 控制台检测已绕过</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            text-align: center;
            max-width: 600px;
            width: 90%;
        }
        
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        
        .status {
            padding: 15px;
            margin: 20px 0;
            border-radius: 8px;
            font-weight: bold;
        }
        
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 2px solid #c3e6cb;
        }
        
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 2px solid #ffeaa7;
        }
        
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 2px solid #f5c6cb;
        }
        
        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .game-area {
            margin-top: 20px;
            padding: 20px;
            border: 2px dashed #ddd;
            border-radius: 10px;
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f8f9fa;
        }
        
        .instructions {
            text-align: left;
            margin-top: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 8px;
        }
        
        .instructions h3 {
            margin-top: 0;
            color: #495057;
        }
        
        .instructions ol {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .instructions li {
            margin: 5px 0;
            color: #6c757d;
        }
        
        #console-log {
            background-color: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            text-align: left;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 15px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 H5游戏控制台检测绕过</h1>
        
        <div id="status" class="status warning">
            ⚠️ 正在初始化绕过脚本...
        </div>
        
        <div>
            <button id="loadGameBtn" onclick="loadGame()" disabled>加载游戏</button>
            <button onclick="testBypass()">测试绕过效果</button>
            <button onclick="openDevTools()">安全打开控制台</button>
            <button onclick="clearLog()">清空日志</button>
        </div>
        
        <div class="game-area" id="gameArea">
            <p style="color: #6c757d;">游戏将在这里加载...</p>
        </div>
        
        <div id="console-log"></div>
        
        <div class="instructions">
            <h3>📋 使用说明</h3>
            <ol>
                <li>页面加载时会自动应用控制台检测绕过脚本</li>
                <li>等待状态显示为"绕过脚本已激活"后，点击"加载游戏"</li>
                <li>现在可以安全地按F12打开开发者工具，不会触发检测</li>
                <li>如果遇到问题，可以点击"测试绕过效果"检查状态</li>
            </ol>
            
            <h3>🔧 技术原理</h3>
            <p>该绕过脚本通过以下方式工作：</p>
            <ul>
                <li>重写 <code>Function.prototype.toString</code> 方法</li>
                <li>拦截 <code>setInterval</code> 和 <code>setTimeout</code> 中的检测代码</li>
                <li>过滤危险的 <code>eval</code> 和 <code>Function</code> 构造调用</li>
                <li>保护 <code>constructor</code> 属性访问</li>
                <li>静默处理检测相关的错误</li>
            </ul>
        </div>
    </div>

    <!-- 首先加载绕过脚本 -->
    <script src="bypass-console-detection.js"></script>
    <!-- 然后加载webpack游戏加载器 -->
    <script src="webpack-game-loader.js"></script>

    <script>
        let gameLoaded = false;
        
        function log(message, type = 'info') {
            const logElement = document.getElementById('console-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            
            // 同时输出到控制台
            console.log(`[页面] ${message}`);
        }
        
        function updateStatus(message, type = 'success') {
            const statusElement = document.getElementById('status');
            const icons = {
                success: '✅',
                warning: '⚠️',
                error: '❌'
            };
            
            statusElement.className = `status ${type}`;
            statusElement.textContent = `${icons[type]} ${message}`;
        }
        
        async function loadGame() {
            if (gameLoaded) {
                log('游戏已经加载过了');
                return;
            }

            try {
                log('开始使用安全加载器加载游戏...');
                updateStatus('正在加载游戏...', 'warning');

                // 使用webpack游戏加载器
                if (!window.gameLoader) {
                    throw new Error('游戏加载器未初始化');
                }

                // 确保绕过措施已应用
                if (!window.gameLoader.applyBypass()) {
                    throw new Error('绕过措施应用失败');
                }

                // 加载游戏
                const gameModule = await window.gameLoader.loadGame('game.js');

                gameLoaded = true;
                log('✅ 游戏加载成功！');
                updateStatus('游戏加载成功，控制台检测已被绕过！', 'success');

                // 更新游戏区域
                const gameArea = document.getElementById('gameArea');
                gameArea.innerHTML = `
                    <div style="color: #28a745; font-weight: bold; text-align: center;">
                        <p>🎮 游戏已安全加载！</p>
                        <p>现在可以安全使用开发者工具了</p>
                        <p style="font-size: 12px; color: #6c757d; margin-top: 10px;">
                            游戏模块: ${gameModule ? '已提取' : '脚本已加载'}
                        </p>
                    </div>
                `;

            } catch (error) {
                log(`❌ 加载游戏时出错: ${error.message}`);
                updateStatus(`加载失败: ${error.message}`, 'error');

                // 显示详细错误信息
                const gameArea = document.getElementById('gameArea');
                gameArea.innerHTML = `
                    <div style="color: #dc3545; text-align: center;">
                        <p>❌ 游戏加载失败</p>
                        <p style="font-size: 12px;">${error.message}</p>
                        <p style="font-size: 10px; color: #6c757d; margin-top: 10px;">
                            请检查game.js文件是否存在，或查看控制台获取更多信息
                        </p>
                    </div>
                `;
            }
        }
        
        function testBypass() {
            log('🧪 开始测试绕过效果...');

            try {
                // 使用绕过工具的测试方法
                if (window.bypassUtils && window.bypassUtils.test) {
                    const bypassResult = window.bypassUtils.test();
                    log(`绕过工具测试结果: ${bypassResult ? '✅ 通过' : '❌ 失败'}`);
                }

                // 使用游戏加载器的测试方法
                if (window.gameLoader && window.gameLoader.test) {
                    const loaderResult = window.gameLoader.test();
                    log(`游戏加载器测试结果: ${loaderResult ? '✅ 通过' : '❌ 失败'}`);
                }

                // 基础测试
                const testFunc = function() { return 'test'; };
                log(`Function.toString测试: ${testFunc.toString().substring(0, 30)}...`);

                const obj = {};
                log(`Constructor访问测试: ${typeof obj.constructor}`);

                log(`Console对象测试: ${typeof console.log}`);

                // webpack环境测试
                if (window.self && window.self.webpackChunk_lodash_modules) {
                    log('Webpack环境测试: ✅ 检测到webpack模块环境');
                } else {
                    log('Webpack环境测试: ⚠️ 未检测到webpack模块');
                }

                log('✅ 绕过效果测试完成！');
                updateStatus('绕过效果测试通过', 'success');

            } catch (error) {
                log(`❌ 测试过程中出错: ${error.message}`);
                updateStatus(`测试失败: ${error.message}`, 'error');
            }
        }
        
        function openDevTools() {
            log('💡 提示：请按F12或右键选择"检查元素"打开开发者工具');
            log('现在打开控制台应该是安全的，不会触发游戏的检测机制');
            updateStatus('可以安全打开开发者工具了', 'success');
        }
        
        function clearLog() {
            document.getElementById('console-log').textContent = '';
            log('日志已清空');
        }
        
        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            log('页面加载完成');

            // 检查绕过脚本是否已加载
            if (window.bypassUtils) {
                log('✅ 绕过脚本已成功加载');
            } else {
                log('❌ 绕过脚本加载失败');
                updateStatus('绕过脚本加载失败', 'error');
                return;
            }

            // 检查游戏加载器是否已加载
            if (window.gameLoader) {
                log('✅ 游戏加载器已成功加载');
                updateStatus('所有组件已就绪，可以加载游戏了', 'success');
                document.getElementById('loadGameBtn').disabled = false;
            } else {
                log('❌ 游戏加载器加载失败');
                updateStatus('游戏加载器加载失败', 'error');
            }
        });
        
        // 监听全局错误
        window.addEventListener('error', function(event) {
            if (event.error && event.error.message) {
                log(`❌ 全局错误: ${event.error.message}`);
            }
        });
        
        // 监听未处理的Promise拒绝
        window.addEventListener('unhandledrejection', function(event) {
            log(`❌ 未处理的Promise拒绝: ${event.reason}`);
        });
    </script>
</body>
</html>
