/**
 * Webpack游戏模块安全加载器
 * 专门用于加载webpack打包的H5游戏，并绕过控制台检测
 */

class WebpackGameLoader {
    constructor() {
        this.isLoaded = false;
        this.gameModule = null;
        this.bypassApplied = false;
        this.loadCallbacks = [];
        
        console.log('[游戏加载器] 初始化完成');
    }
    
    // 应用绕过措施
    applyBypass() {
        if (this.bypassApplied) {
            console.log('[游戏加载器] 绕过措施已经应用过了');
            return true;
        }
        
        try {
            // 确保绕过脚本已加载
            if (typeof window.bypassUtils === 'undefined') {
                console.error('[游戏加载器] 绕过脚本未加载，请先加载bypass-console-detection.js');
                return false;
            }
            
            // 额外的webpack特定绕过
            this.setupWebpackBypass();
            
            this.bypassApplied = true;
            console.log('[游戏加载器] 绕过措施应用成功');
            return true;
            
        } catch (error) {
            console.error('[游戏加载器] 应用绕过措施失败:', error);
            return false;
        }
    }
    
    // 设置webpack特定的绕过
    setupWebpackBypass() {
        // 1. 拦截模块定义
        const originalDefine = window.define;
        if (typeof originalDefine === 'function') {
            window.define = function() {
                try {
                    return originalDefine.apply(this, arguments);
                } catch (error) {
                    console.log('[游戏加载器] 拦截到模块定义错误:', error.message);
                    return;
                }
            };
        }
        
        // 2. 保护全局对象
        const protectGlobalObject = (obj, name) => {
            if (obj && typeof obj === 'object') {
                return new Proxy(obj, {
                    get: function(target, prop) {
                        try {
                            return target[prop];
                        } catch (error) {
                            console.log(`[游戏加载器] 拦截到${name}.${prop}访问错误:`, error.message);
                            return undefined;
                        }
                    },
                    set: function(target, prop, value) {
                        try {
                            target[prop] = value;
                            return true;
                        } catch (error) {
                            console.log(`[游戏加载器] 拦截到${name}.${prop}设置错误:`, error.message);
                            return false;
                        }
                    }
                });
            }
            return obj;
        };
        
        // 保护关键全局对象
        if (window.self) {
            window.self = protectGlobalObject(window.self, 'self');
        }
        
        // 3. 拦截CryptoJS相关错误
        window.addEventListener('error', (event) => {
            if (event.error && event.error.message) {
                const message = event.error.message;
                if (message.includes('constructor') || 
                    message.includes('toString') ||
                    message.includes('not a function') ||
                    message.includes('CryptoJS')) {
                    console.log('[游戏加载器] 拦截到游戏检测错误:', message);
                    event.preventDefault();
                    event.stopPropagation();
                    return false;
                }
            }
        }, true);
        
        console.log('[游戏加载器] webpack特定绕过设置完成');
    }
    
    // 安全加载游戏脚本
    async loadGame(scriptPath = 'game.js') {
        if (this.isLoaded) {
            console.log('[游戏加载器] 游戏已经加载过了');
            return this.gameModule;
        }
        
        if (!this.bypassApplied) {
            console.log('[游戏加载器] 自动应用绕过措施...');
            if (!this.applyBypass()) {
                throw new Error('绕过措施应用失败');
            }
        }
        
        return new Promise((resolve, reject) => {
            console.log('[游戏加载器] 开始加载游戏脚本:', scriptPath);
            
            // 创建script标签
            const script = document.createElement('script');
            script.src = scriptPath + '?v=' + Date.now();
            script.type = 'text/javascript';
            
            // 设置加载成功回调
            script.onload = () => {
                console.log('[游戏加载器] 游戏脚本加载成功');
                this.isLoaded = true;
                
                // 等待一小段时间让模块初始化
                setTimeout(() => {
                    this.extractGameModule();
                    resolve(this.gameModule);
                    this.notifyCallbacks(null, this.gameModule);
                }, 100);
            };
            
            // 设置加载失败回调
            script.onerror = (error) => {
                console.error('[游戏加载器] 游戏脚本加载失败:', error);
                const errorMsg = '游戏脚本加载失败，请检查文件路径';
                reject(new Error(errorMsg));
                this.notifyCallbacks(new Error(errorMsg), null);
            };
            
            // 添加到页面
            document.head.appendChild(script);
        });
    }
    
    // 提取游戏模块
    extractGameModule() {
        try {
            // 尝试从webpack chunk中提取游戏模块
            if (window.self && window.self.webpackChunk_lodash_modules) {
                console.log('[游戏加载器] 检测到webpack模块');
                this.gameModule = window.self.webpackChunk_lodash_modules;
            }
            
            // 检查是否有其他可能的游戏对象
            const possibleGameObjects = ['game', 'Game', 'gameInstance', 'app', 'App'];
            for (let objName of possibleGameObjects) {
                if (window[objName]) {
                    console.log(`[游戏加载器] 发现游戏对象: ${objName}`);
                    this.gameModule = this.gameModule || window[objName];
                }
            }
            
            if (this.gameModule) {
                console.log('[游戏加载器] 游戏模块提取成功');
            } else {
                console.log('[游戏加载器] 未找到明确的游戏模块，但脚本已加载');
            }
            
        } catch (error) {
            console.error('[游戏加载器] 提取游戏模块时出错:', error);
        }
    }
    
    // 添加加载回调
    onLoad(callback) {
        if (typeof callback === 'function') {
            if (this.isLoaded) {
                callback(null, this.gameModule);
            } else {
                this.loadCallbacks.push(callback);
            }
        }
    }
    
    // 通知所有回调
    notifyCallbacks(error, module) {
        this.loadCallbacks.forEach(callback => {
            try {
                callback(error, module);
            } catch (e) {
                console.error('[游戏加载器] 回调执行错误:', e);
            }
        });
        this.loadCallbacks = [];
    }
    
    // 检查游戏是否已加载
    isGameLoaded() {
        return this.isLoaded;
    }
    
    // 获取游戏模块
    getGameModule() {
        return this.gameModule;
    }
    
    // 重置加载器状态
    reset() {
        this.isLoaded = false;
        this.gameModule = null;
        this.loadCallbacks = [];
        console.log('[游戏加载器] 状态已重置');
    }
    
    // 测试功能
    test() {
        console.log('[游戏加载器] 开始测试...');
        
        const tests = [
            {
                name: '绕过脚本检查',
                test: () => typeof window.bypassUtils !== 'undefined'
            },
            {
                name: 'Function.toString检查',
                test: () => {
                    const func = function() {};
                    return func.toString().length > 0;
                }
            },
            {
                name: 'Console对象检查',
                test: () => typeof console.log === 'function'
            },
            {
                name: 'Webpack环境检查',
                test: () => window.self && typeof window.self === 'object'
            }
        ];
        
        let passed = 0;
        tests.forEach(test => {
            try {
                const result = test.test();
                console.log(`[游戏加载器] ${test.name}: ${result ? '✅ 通过' : '❌ 失败'}`);
                if (result) passed++;
            } catch (error) {
                console.log(`[游戏加载器] ${test.name}: ❌ 错误 - ${error.message}`);
            }
        });
        
        console.log(`[游戏加载器] 测试完成: ${passed}/${tests.length} 通过`);
        return passed === tests.length;
    }
}

// 创建全局实例
window.gameLoader = new WebpackGameLoader();

console.log('[游戏加载器] 全局实例已创建，使用 window.gameLoader 访问');
