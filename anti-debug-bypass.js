/**
 * 强力反调试绕过脚本
 * 专门处理页面跳转、无限循环、强制刷新等反调试机制
 */

(function() {
    'use strict';
    
    console.log('[反调试绕过] 开始初始化强力反调试绕过...');
    
    // 保存原始方法
    const originalMethods = {
        // 页面导航相关
        assign: window.location.assign,
        replace: window.location.replace,
        reload: window.location.reload,
        
        // 窗口操作相关
        open: window.open,
        close: window.close,
        
        // 文档操作相关
        write: document.write,
        writeln: document.writeln,
        
        // 历史记录相关
        pushState: window.history.pushState,
        replaceState: window.history.replaceState,
        go: window.history.go,
        back: window.history.back,
        forward: window.history.forward,
        
        // 其他
        setTimeout: window.setTimeout,
        setInterval: window.setInterval,
        requestAnimationFrame: window.requestAnimationFrame
    };
    
    // 1. 拦截页面跳转
    const interceptPageNavigation = () => {
        // 拦截 location.href 设置
        let currentHref = window.location.href;
        Object.defineProperty(window.location, 'href', {
            get: function() {
                return currentHref;
            },
            set: function(value) {
                console.log('[反调试绕过] 拦截到页面跳转尝试:', value);
                
                // 检查是否是恶意跳转
                if (value.includes('about:blank') || 
                    value.includes('javascript:') ||
                    value === '' ||
                    value === 'about:blank') {
                    console.log('[反调试绕过] 阻止恶意页面跳转');
                    return;
                }
                
                // 允许正常跳转
                currentHref = value;
                originalMethods.assign.call(window.location, value);
            }
        });
        
        // 拦截 location.assign
        window.location.assign = function(url) {
            console.log('[反调试绕过] 拦截到 location.assign:', url);
            if (url && (url.includes('about:blank') || url === '')) {
                console.log('[反调试绕过] 阻止 location.assign 恶意跳转');
                return;
            }
            return originalMethods.assign.call(this, url);
        };
        
        // 拦截 location.replace
        window.location.replace = function(url) {
            console.log('[反调试绕过] 拦截到 location.replace:', url);
            if (url && (url.includes('about:blank') || url === '')) {
                console.log('[反调试绕过] 阻止 location.replace 恶意跳转');
                return;
            }
            return originalMethods.replace.call(this, url);
        };
        
        // 拦截 location.reload
        window.location.reload = function(force) {
            console.log('[反调试绕过] 拦截到页面刷新尝试');
            // 可以选择性地阻止刷新
            // return originalMethods.reload.call(this, force);
            console.log('[反调试绕过] 阻止页面刷新');
        };
    };
    
    // 2. 拦截窗口操作
    const interceptWindowOperations = () => {
        // 拦截 window.open
        window.open = function(url, name, features) {
            console.log('[反调试绕过] 拦截到 window.open:', url, name, features);
            if (url && (url.includes('about:blank') || url === '')) {
                console.log('[反调试绕过] 阻止打开空白窗口');
                return {
                    close: function() {},
                    focus: function() {},
                    blur: function() {}
                };
            }
            return originalMethods.open.call(this, url, name, features);
        };
        
        // 拦截 window.close
        window.close = function() {
            console.log('[反调试绕过] 拦截到窗口关闭尝试');
            // 阻止窗口关闭
            console.log('[反调试绕过] 阻止窗口关闭');
        };
    };
    
    // 3. 拦截文档操作
    const interceptDocumentOperations = () => {
        // 拦截 document.write
        document.write = function(content) {
            console.log('[反调试绕过] 拦截到 document.write:', content);
            if (content && (content.includes('about:blank') || content.trim() === '')) {
                console.log('[反调试绕过] 阻止恶意 document.write');
                return;
            }
            return originalMethods.write.call(this, content);
        };
        
        // 拦截 document.writeln
        document.writeln = function(content) {
            console.log('[反调试绕过] 拦截到 document.writeln:', content);
            if (content && (content.includes('about:blank') || content.trim() === '')) {
                console.log('[反调试绕过] 阻止恶意 document.writeln');
                return;
            }
            return originalMethods.writeln.call(this, content);
        };
    };
    
    // 4. 拦截历史记录操作
    const interceptHistoryOperations = () => {
        // 拦截 history.pushState
        window.history.pushState = function(state, title, url) {
            console.log('[反调试绕过] 拦截到 history.pushState:', state, title, url);
            if (url && (url.includes('about:blank') || url === '')) {
                console.log('[反调试绕过] 阻止恶意 history.pushState');
                return;
            }
            return originalMethods.pushState.call(this, state, title, url);
        };
        
        // 拦截 history.replaceState
        window.history.replaceState = function(state, title, url) {
            console.log('[反调试绕过] 拦截到 history.replaceState:', state, title, url);
            if (url && (url.includes('about:blank') || url === '')) {
                console.log('[反调试绕过] 阻止恶意 history.replaceState');
                return;
            }
            return originalMethods.replaceState.call(this, state, title, url);
        };
        
        // 拦截其他历史操作
        window.history.go = function(delta) {
            console.log('[反调试绕过] 拦截到 history.go:', delta);
            // 可以选择性地阻止
            return originalMethods.go.call(this, delta);
        };
        
        window.history.back = function() {
            console.log('[反调试绕过] 拦截到 history.back');
            return originalMethods.back.call(this);
        };
        
        window.history.forward = function() {
            console.log('[反调试绕过] 拦截到 history.forward');
            return originalMethods.forward.call(this);
        };
    };
    
    // 5. 拦截定时器中的恶意代码
    const interceptTimers = () => {
        // 重写 setTimeout
        window.setTimeout = function(callback, delay) {
            if (typeof callback === 'function') {
                const wrappedCallback = function() {
                    try {
                        return callback.apply(this, arguments);
                    } catch (error) {
                        console.log('[反调试绕过] 拦截到定时器中的错误:', error.message);
                        // 检查是否是反调试相关的错误
                        if (error.message.includes('location') ||
                            error.message.includes('href') ||
                            error.message.includes('blank')) {
                            console.log('[反调试绕过] 阻止定时器中的反调试代码');
                            return;
                        }
                        throw error;
                    }
                };
                return originalMethods.setTimeout.call(this, wrappedCallback, delay);
            } else if (typeof callback === 'string') {
                // 检查字符串代码
                if (callback.includes('location') || 
                    callback.includes('href') ||
                    callback.includes('about:blank')) {
                    console.log('[反调试绕过] 阻止定时器中的恶意字符串代码:', callback);
                    return originalMethods.setTimeout.call(this, function() {}, delay);
                }
            }
            return originalMethods.setTimeout.apply(this, arguments);
        };
        
        // 重写 setInterval
        window.setInterval = function(callback, delay) {
            if (typeof callback === 'function') {
                const wrappedCallback = function() {
                    try {
                        return callback.apply(this, arguments);
                    } catch (error) {
                        console.log('[反调试绕过] 拦截到定时器中的错误:', error.message);
                        if (error.message.includes('location') ||
                            error.message.includes('href') ||
                            error.message.includes('blank')) {
                            console.log('[反调试绕过] 阻止定时器中的反调试代码');
                            return;
                        }
                        throw error;
                    }
                };
                return originalMethods.setInterval.call(this, wrappedCallback, delay);
            } else if (typeof callback === 'string') {
                if (callback.includes('location') || 
                    callback.includes('href') ||
                    callback.includes('about:blank')) {
                    console.log('[反调试绕过] 阻止定时器中的恶意字符串代码:', callback);
                    return originalMethods.setInterval.call(this, function() {}, delay);
                }
            }
            return originalMethods.setInterval.apply(this, arguments);
        };
    };
    
    // 6. 监听和拦截页面卸载事件
    const interceptUnloadEvents = () => {
        // 拦截 beforeunload 事件
        window.addEventListener('beforeunload', function(event) {
            console.log('[反调试绕过] 拦截到页面卸载事件');
            event.preventDefault();
            event.returnValue = '';
            return '';
        }, true);
        
        // 拦截 unload 事件
        window.addEventListener('unload', function(event) {
            console.log('[反调试绕过] 拦截到页面卸载事件');
            event.preventDefault();
        }, true);
    };
    
    // 7. 应用所有拦截措施
    const applyAllInterceptions = () => {
        interceptPageNavigation();
        interceptWindowOperations();
        interceptDocumentOperations();
        interceptHistoryOperations();
        interceptTimers();
        interceptUnloadEvents();
        
        console.log('[反调试绕过] 所有拦截措施已应用');
    };
    
    // 8. 立即应用拦截
    applyAllInterceptions();
    
    // 9. 定期重新应用拦截（防止被覆盖）
    setInterval(() => {
        applyAllInterceptions();
    }, 1000);
    
    // 10. 导出恢复方法
    window.antiDebugBypass = {
        restore: function() {
            // 恢复原始方法
            window.location.assign = originalMethods.assign;
            window.location.replace = originalMethods.replace;
            window.location.reload = originalMethods.reload;
            window.open = originalMethods.open;
            window.close = originalMethods.close;
            document.write = originalMethods.write;
            document.writeln = originalMethods.writeln;
            window.history.pushState = originalMethods.pushState;
            window.history.replaceState = originalMethods.replaceState;
            window.history.go = originalMethods.go;
            window.history.back = originalMethods.back;
            window.history.forward = originalMethods.forward;
            window.setTimeout = originalMethods.setTimeout;
            window.setInterval = originalMethods.setInterval;
            
            console.log('[反调试绕过] 已恢复所有原始方法');
        },
        
        test: function() {
            console.log('[反调试绕过] 开始测试反调试绕过...');
            
            // 测试页面跳转拦截
            try {
                window.location.href = 'about:blank';
                console.log('[反调试绕过] 页面跳转拦截测试: ✅ 通过');
            } catch (error) {
                console.log('[反调试绕过] 页面跳转拦截测试: ❌ 失败');
            }
            
            // 测试窗口操作拦截
            try {
                const popup = window.open('about:blank');
                console.log('[反调试绕过] 窗口操作拦截测试: ✅ 通过');
            } catch (error) {
                console.log('[反调试绕过] 窗口操作拦截测试: ❌ 失败');
            }
            
            console.log('[反调试绕过] 测试完成');
            return true;
        }
    };
    
    console.log('[反调试绕过] 强力反调试绕过初始化完成！');
    console.log('[反调试绕过] 现在可以安全地打开开发者工具，不会被跳转到空白页');
    
})();
