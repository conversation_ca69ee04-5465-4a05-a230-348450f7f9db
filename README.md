# H5游戏控制台检测绕过工具

## 问题描述

您的H5游戏在打开浏览器控制台（F12）时会卡住并报错：
```
Uncaught TypeError: (intermediate value)["constructor"](...) is not a function
```

这是因为游戏中包含了控制台检测代码，当检测到开发者工具打开时会故意抛出错误来阻止调试。

## 解决方案

本工具提供了完整的控制台检测绕过方案，包含以下文件：

### 文件说明

1. **`bypass-console-detection.js`** - 核心绕过脚本
   - 重写 `Function.prototype.toString` 方法
   - 拦截 `setInterval` 和 `setTimeout` 中的检测代码
   - 过滤危险的 `eval` 和 `Function` 构造调用
   - 保护 `constructor` 属性访问
   - 静默处理检测相关的错误

2. **`index.html`** - 主页面，集成了绕过脚本
   - 自动加载绕过脚本
   - 提供友好的用户界面
   - 包含测试和调试功能

3. **`test.html`** - 测试页面
   - 用于测试各种绕过方案
   - 提供详细的调试信息

## 使用方法

### 方法一：使用集成页面（推荐）

1. 将所有文件放在同一目录下
2. 打开 `index.html`
3. 等待绕过脚本加载完成
4. 点击"加载游戏"按钮
5. 现在可以安全地按F12打开开发者工具

### 方法二：手动集成到现有页面

在您的HTML页面中，在加载 `game.js` 之前先加载绕过脚本：

```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>您的游戏</title>
</head>
<body>
    <!-- 首先加载绕过脚本 -->
    <script src="bypass-console-detection.js"></script>
    
    <!-- 然后加载游戏文件 -->
    <script src="game.js"></script>
    
    <!-- 您的其他代码 -->
</body>
</html>
```

### 方法三：直接在控制台中运行

如果您想在已经打开的页面中应用绕过：

1. 在页面加载完成但未打开控制台时，将 `bypass-console-detection.js` 的内容复制到地址栏
2. 在地址栏前加上 `javascript:` 前缀
3. 按回车执行
4. 现在可以安全打开控制台

## 技术原理

### 常见的控制台检测方法

1. **Function.toString检测**
   ```javascript
   // 检测代码示例
   (function(){}).constructor("debugger")();
   ```

2. **时间差检测**
   ```javascript
   // 通过console.log执行时间差检测控制台是否打开
   ```

3. **定时器检测**
   ```javascript
   setInterval(function(){
       // 在定时器中执行检测代码
   }, 1000);
   ```

### 绕过原理

1. **重写关键方法**：替换 `Function.prototype.toString` 等关键方法
2. **错误拦截**：捕获并静默处理检测代码产生的错误
3. **代码过滤**：识别并阻止危险代码的执行
4. **属性保护**：保护关键对象属性不被恶意访问

## 测试功能

使用 `index.html` 中的测试功能来验证绕过是否有效：

1. 点击"测试绕过效果"按钮
2. 查看控制台输出
3. 尝试打开开发者工具
4. 检查是否还会出现错误

## 注意事项

1. **加载顺序很重要**：必须在游戏脚本之前加载绕过脚本
2. **兼容性**：支持现代浏览器（Chrome、Firefox、Safari、Edge）
3. **性能影响**：绕过脚本对性能影响很小
4. **安全性**：仅用于调试目的，不会影响游戏正常功能

## 故障排除

### 如果绕过不生效

1. 确认绕过脚本在游戏脚本之前加载
2. 检查浏览器控制台是否有其他错误
3. 尝试刷新页面重新加载
4. 使用 `test.html` 进行详细测试

### 常见错误

1. **"bypassUtils is not defined"**
   - 绕过脚本未正确加载
   - 检查文件路径和网络连接

2. **仍然出现检测错误**
   - 可能存在新的检测方法
   - 尝试更新绕过脚本

## 高级用法

### 自定义配置

您可以修改 `bypass-console-detection.js` 中的配置：

```javascript
// 在脚本开头添加配置
const config = {
    enableLogging: true,        // 是否启用日志
    strictMode: false,          // 是否使用严格模式
    autoCleanup: true          // 是否自动清理
};
```

### API接口

绕过脚本提供了一些有用的API：

```javascript
// 测试绕过是否有效
window.bypassUtils.test();

// 恢复原始方法
window.bypassUtils.restore();
```

## 更新日志

- **v1.0.0** - 初始版本，支持基本的控制台检测绕过
- 支持Function.toString检测绕过
- 支持定时器检测绕过
- 支持错误拦截和过滤

## 许可证

本工具仅供学习和调试使用，请遵守相关法律法规。

## 支持

如果您遇到问题或需要帮助，请检查：

1. 浏览器控制台的错误信息
2. 网络连接是否正常
3. 文件路径是否正确
4. 是否按照正确顺序加载脚本
