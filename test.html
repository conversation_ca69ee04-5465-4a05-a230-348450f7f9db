<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>H5游戏控制台检测分析</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        #console-output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>H5游戏控制台检测分析工具</h1>
        
        <div class="status warning">
            <strong>注意：</strong>请在打开开发者工具之前先点击"应用绕过方案"按钮
        </div>
        
        <div id="status-display" class="status">
            <strong>状态：</strong>等待操作...
        </div>
        
        <div>
            <button onclick="applyBypass()">应用绕过方案</button>
            <button onclick="loadOriginalGame()">加载原始游戏</button>
            <button onclick="testConsoleDetection()">测试控制台检测</button>
            <button onclick="clearOutput()">清空输出</button>
        </div>
        
        <div id="console-output"></div>
        
        <div>
            <h3>绕过方案说明：</h3>
            <ul>
                <li><strong>方案1：</strong>重写Function.prototype.toString方法</li>
                <li><strong>方案2：</strong>禁用setInterval和setTimeout中的控制台检测</li>
                <li><strong>方案3：</strong>重写console对象方法</li>
                <li><strong>方案4：</strong>拦截constructor属性访问</li>
            </ul>
        </div>
    </div>

    <script>
        let originalConsole = {};
        let bypassApplied = false;
        
        // 保存原始console方法
        Object.keys(console).forEach(key => {
            if (typeof console[key] === 'function') {
                originalConsole[key] = console[key].bind(console);
            }
        });
        
        function log(message, type = 'info') {
            const output = document.getElementById('console-output');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            output.textContent += logEntry;
            output.scrollTop = output.scrollHeight;
            
            // 同时输出到真实控制台（如果可用）
            if (originalConsole[type]) {
                originalConsole[type](message);
            }
        }
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status-display');
            statusDiv.className = `status ${type === 'error' ? 'error' : type === 'success' ? 'success' : 'warning'}`;
            statusDiv.innerHTML = `<strong>状态：</strong>${message}`;
        }
        
        function applyBypass() {
            try {
                log('开始应用控制台检测绕过方案...');
                
                // 方案1: 重写Function.prototype.toString
                const originalToString = Function.prototype.toString;
                Function.prototype.toString = function() {
                    if (this === Function.prototype.toString) {
                        return 'function toString() { [native code] }';
                    }
                    return originalToString.call(this);
                };
                log('✓ 已重写Function.prototype.toString');
                
                // 方案2: 拦截setInterval和setTimeout
                const originalSetInterval = window.setInterval;
                const originalSetTimeout = window.setTimeout;
                
                window.setInterval = function(callback, delay) {
                    if (typeof callback === 'function') {
                        const wrappedCallback = function() {
                            try {
                                return callback.apply(this, arguments);
                            } catch (e) {
                                log(`拦截到setInterval错误: ${e.message}`, 'warning');
                                return;
                            }
                        };
                        return originalSetInterval.call(this, wrappedCallback, delay);
                    }
                    return originalSetInterval.apply(this, arguments);
                };
                
                window.setTimeout = function(callback, delay) {
                    if (typeof callback === 'function') {
                        const wrappedCallback = function() {
                            try {
                                return callback.apply(this, arguments);
                            } catch (e) {
                                log(`拦截到setTimeout错误: ${e.message}`, 'warning');
                                return;
                            }
                        };
                        return originalSetTimeout.call(this, wrappedCallback, delay);
                    }
                    return originalSetTimeout.apply(this, arguments);
                };
                log('✓ 已拦截setInterval和setTimeout');
                
                // 方案3: 保护console对象
                const consoleProxy = new Proxy(console, {
                    get: function(target, prop) {
                        if (typeof target[prop] === 'function') {
                            return function() {
                                try {
                                    return target[prop].apply(target, arguments);
                                } catch (e) {
                                    log(`Console方法调用被拦截: ${prop}`, 'warning');
                                    return;
                                }
                            };
                        }
                        return target[prop];
                    }
                });
                
                // 方案4: 拦截constructor访问
                const originalDefineProperty = Object.defineProperty;
                Object.defineProperty = function(obj, prop, descriptor) {
                    if (prop === 'constructor' && descriptor && typeof descriptor.get === 'function') {
                        log('拦截到constructor属性定义', 'warning');
                        descriptor.get = function() {
                            return function() { return {}; };
                        };
                    }
                    return originalDefineProperty.call(this, obj, prop, descriptor);
                };
                log('✓ 已设置constructor拦截');
                
                // 方案5: 重写eval和Function构造器
                const originalEval = window.eval;
                window.eval = function(code) {
                    if (typeof code === 'string' && (
                        code.includes('constructor') || 
                        code.includes('toString') ||
                        code.includes('debugger') ||
                        code.includes('console')
                    )) {
                        log(`拦截到可疑eval代码: ${code.substring(0, 100)}...`, 'warning');
                        return;
                    }
                    return originalEval.call(this, code);
                };
                log('✓ 已拦截eval');
                
                bypassApplied = true;
                updateStatus('绕过方案已成功应用！现在可以安全打开开发者工具', 'success');
                log('所有绕过方案已成功应用！', 'success');
                
            } catch (error) {
                log(`应用绕过方案时出错: ${error.message}`, 'error');
                updateStatus(`绕过方案应用失败: ${error.message}`, 'error');
            }
        }
        
        function loadOriginalGame() {
            if (!bypassApplied) {
                updateStatus('请先应用绕过方案！', 'error');
                return;
            }
            
            try {
                log('开始加载原始游戏文件...');
                
                // 创建script标签加载game.js
                const script = document.createElement('script');
                script.src = 'game.js?v=' + Date.now();
                script.onload = function() {
                    log('游戏文件加载成功！', 'success');
                    updateStatus('游戏已加载，控制台检测已被绕过', 'success');
                };
                script.onerror = function() {
                    log('游戏文件加载失败！', 'error');
                    updateStatus('游戏文件加载失败', 'error');
                };
                
                document.head.appendChild(script);
                
            } catch (error) {
                log(`加载游戏时出错: ${error.message}`, 'error');
                updateStatus(`加载失败: ${error.message}`, 'error');
            }
        }
        
        function testConsoleDetection() {
            log('开始测试控制台检测...');
            
            // 测试各种可能触发检测的操作
            try {
                // 测试Function.prototype.toString
                log('测试Function.prototype.toString...');
                const testFunc = function() {};
                log(`toString结果: ${testFunc.toString()}`);
                
                // 测试constructor访问
                log('测试constructor访问...');
                const obj = {};
                log(`constructor类型: ${typeof obj.constructor}`);
                
                // 测试console对象
                log('测试console对象访问...');
                log(`console.log类型: ${typeof console.log}`);
                
                updateStatus('控制台检测测试完成，未发现异常', 'success');
                
            } catch (error) {
                log(`测试过程中出错: ${error.message}`, 'error');
                updateStatus(`测试失败: ${error.message}`, 'error');
            }
        }
        
        function clearOutput() {
            document.getElementById('console-output').textContent = '';
            log('输出已清空');
        }
        
        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            log('页面加载完成，准备就绪');
            updateStatus('请点击"应用绕过方案"按钮开始', 'warning');
        });
        
        // 监听错误事件
        window.addEventListener('error', function(event) {
            log(`全局错误: ${event.error ? event.error.message : event.message}`, 'error');
        });
        
        // 监听未处理的Promise拒绝
        window.addEventListener('unhandledrejection', function(event) {
            log(`未处理的Promise拒绝: ${event.reason}`, 'error');
        });
    </script>
</body>
</html>
