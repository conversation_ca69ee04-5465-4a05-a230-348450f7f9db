/**
 * H5游戏控制台检测绕过脚本
 * 用于绕过常见的控制台检测机制
 * 特别针对webpack打包的游戏模块
 */

(function() {
    'use strict';

    console.log('[绕过脚本] 开始初始化控制台检测绕过...');

    // 保存原始方法的引用
    const originalMethods = {
        toString: Function.prototype.toString,
        setInterval: window.setInterval,
        setTimeout: window.setTimeout,
        eval: window.eval,
        Function: window.Function,
        defineProperty: Object.defineProperty,
        getOwnPropertyDescriptor: Object.getOwnPropertyDescriptor,
        push: Array.prototype.push
    };

    // 特殊处理webpack模块加载
    let webpackChunkIntercepted = false;
    
    // 1. 重写Function.prototype.toString方法
    // 这是最常见的控制台检测方法
    Function.prototype.toString = function() {
        // 如果是检测toString本身，返回原生代码字符串
        if (this === Function.prototype.toString) {
            return 'function toString() { [native code] }';
        }
        
        // 对于其他函数，检查是否可能是检测代码
        const originalResult = originalMethods.toString.call(this);
        
        // 如果函数内容包含控制台检测相关代码，返回一个安全的版本
        if (originalResult.includes('constructor') && 
            originalResult.includes('toString') &&
            originalResult.length < 100) {
            return 'function() { [native code] }';
        }
        
        return originalResult;
    };
    
    // 2. 拦截和包装setInterval/setTimeout
    // 防止定时器中的检测代码执行
    window.setInterval = function(callback, delay) {
        if (typeof callback === 'function') {
            const wrappedCallback = function() {
                try {
                    return callback.apply(this, arguments);
                } catch (error) {
                    // 静默处理检测代码的错误
                    if (error.message && (
                        error.message.includes('constructor') ||
                        error.message.includes('toString') ||
                        error.message.includes('not a function')
                    )) {
                        console.log('[绕过脚本] 拦截到控制台检测代码，已阻止执行');
                        return;
                    }
                    throw error;
                }
            };
            return originalMethods.setInterval.call(this, wrappedCallback, delay);
        }
        return originalMethods.setInterval.apply(this, arguments);
    };
    
    window.setTimeout = function(callback, delay) {
        if (typeof callback === 'function') {
            const wrappedCallback = function() {
                try {
                    return callback.apply(this, arguments);
                } catch (error) {
                    if (error.message && (
                        error.message.includes('constructor') ||
                        error.message.includes('toString') ||
                        error.message.includes('not a function')
                    )) {
                        console.log('[绕过脚本] 拦截到控制台检测代码，已阻止执行');
                        return;
                    }
                    throw error;
                }
            };
            return originalMethods.setTimeout.call(this, wrappedCallback, delay);
        }
        return originalMethods.setTimeout.apply(this, arguments);
    };
    
    // 3. 重写eval函数，过滤危险代码
    window.eval = function(code) {
        if (typeof code === 'string') {
            // 检测是否包含控制台检测相关代码
            const dangerousPatterns = [
                /constructor.*toString/i,
                /toString.*constructor/i,
                /debugger/i,
                /console.*log.*constructor/i,
                /setInterval.*constructor/i
            ];
            
            for (let pattern of dangerousPatterns) {
                if (pattern.test(code)) {
                    console.log('[绕过脚本] 拦截到危险的eval代码:', code.substring(0, 100) + '...');
                    return undefined;
                }
            }
        }
        return originalMethods.eval.call(this, code);
    };
    
    // 4. 重写Function构造器
    const OriginalFunction = window.Function;
    window.Function = function() {
        const args = Array.prototype.slice.call(arguments);
        const code = args[args.length - 1];
        
        if (typeof code === 'string') {
            const dangerousPatterns = [
                /constructor.*toString/i,
                /toString.*constructor/i,
                /debugger/i
            ];
            
            for (let pattern of dangerousPatterns) {
                if (pattern.test(code)) {
                    console.log('[绕过脚本] 拦截到危险的Function构造:', code.substring(0, 100) + '...');
                    return function() { return undefined; };
                }
            }
        }
        
        return OriginalFunction.apply(this, arguments);
    };
    
    // 保持Function的原型链
    window.Function.prototype = OriginalFunction.prototype;
    
    // 5. 保护Object.defineProperty
    Object.defineProperty = function(obj, prop, descriptor) {
        // 如果尝试定义constructor属性的getter，进行拦截
        if (prop === 'constructor' && descriptor && typeof descriptor.get === 'function') {
            console.log('[绕过脚本] 拦截到constructor属性定义');
            descriptor.get = function() {
                return function() { return {}; };
            };
        }
        
        return originalMethods.defineProperty.call(this, obj, prop, descriptor);
    };
    
    // 6. 创建安全的console代理
    const createConsoleProxy = () => {
        const handler = {
            get: function(target, prop) {
                if (typeof target[prop] === 'function') {
                    return function() {
                        try {
                            return target[prop].apply(target, arguments);
                        } catch (error) {
                            console.log(`[绕过脚本] Console方法 ${prop} 调用被保护`);
                            return undefined;
                        }
                    };
                }
                return target[prop];
            }
        };
        
        return new Proxy(console, handler);
    };
    
    // 7. 重写常见的检测方法
    const bypassCommonDetection = () => {
        // 重写Date.now以防止时间差检测
        const originalDateNow = Date.now;
        Date.now = function() {
            return originalDateNow.call(this);
        };
        
        // 重写performance.now
        if (window.performance && window.performance.now) {
            const originalPerfNow = window.performance.now;
            window.performance.now = function() {
                return originalPerfNow.call(this);
            };
        }
    };
    
    // 8. 监听和拦截错误
    const originalErrorHandler = window.onerror;
    window.onerror = function(message, source, lineno, colno, error) {
        // 如果是控制台检测相关的错误，静默处理
        if (message && (
            message.includes('constructor') ||
            message.includes('toString') ||
            message.includes('not a function')
        )) {
            console.log('[绕过脚本] 拦截到控制台检测错误:', message);
            return true; // 阻止错误冒泡
        }
        
        if (originalErrorHandler) {
            return originalErrorHandler.apply(this, arguments);
        }
        return false;
    };
    
    // 9. 拦截webpack模块加载
    const interceptWebpackChunk = () => {
        if (webpackChunkIntercepted) return;

        // 拦截self.webpackChunk_lodash_modules
        const originalSelf = window.self;
        if (originalSelf && typeof originalSelf === 'object') {
            const createWebpackProxy = (target) => {
                return new Proxy(target || [], {
                    get: function(obj, prop) {
                        if (prop === 'push') {
                            return function(chunk) {
                                console.log('[绕过脚本] 拦截到webpack模块加载:', chunk);
                                try {
                                    return originalMethods.push.call(obj, chunk);
                                } catch (error) {
                                    console.log('[绕过脚本] webpack模块加载错误已被拦截:', error.message);
                                    return obj.length;
                                }
                            };
                        }
                        return obj[prop];
                    }
                });
            };

            // 创建或拦截webpackChunk_lodash_modules
            if (!originalSelf.webpackChunk_lodash_modules) {
                originalSelf.webpackChunk_lodash_modules = createWebpackProxy([]);
            } else {
                originalSelf.webpackChunk_lodash_modules = createWebpackProxy(originalSelf.webpackChunk_lodash_modules);
            }

            webpackChunkIntercepted = true;
            console.log('[绕过脚本] webpack模块加载拦截已设置');
        }
    };

    // 10. 应用所有绕过措施
    bypassCommonDetection();
    interceptWebpackChunk();

    // 11. 定期清理可能的检测定时器
    const cleanupDetectionTimers = () => {
        // 重新应用webpack拦截（防止被覆盖）
        interceptWebpackChunk();
        console.log('[绕过脚本] 执行定期清理...');
    };

    // 每5秒执行一次清理
    setInterval(cleanupDetectionTimers, 5000);

    console.log('[绕过脚本] 控制台检测绕过初始化完成！');
    console.log('[绕过脚本] 现在可以安全地打开开发者工具了');
    
    // 导出一些有用的方法供外部使用
    window.bypassUtils = {
        restore: function() {
            // 恢复原始方法
            Function.prototype.toString = originalMethods.toString;
            window.setInterval = originalMethods.setInterval;
            window.setTimeout = originalMethods.setTimeout;
            window.eval = originalMethods.eval;
            window.Function = originalMethods.Function;
            Object.defineProperty = originalMethods.defineProperty;
            console.log('[绕过脚本] 已恢复所有原始方法');
        },
        
        test: function() {
            // 测试绕过是否有效
            console.log('[绕过脚本] 开始测试...');
            try {
                const testFunc = function() {};
                console.log('Function.toString测试:', testFunc.toString());
                console.log('Constructor测试:', typeof testFunc.constructor);
                console.log('[绕过脚本] 测试完成，绕过有效！');
                return true;
            } catch (error) {
                console.error('[绕过脚本] 测试失败:', error);
                return false;
            }
        }
    };
    
})();
